import { Logger } from '@nestjs/common';
import {
  SSMClient,
  GetParametersByPathCommand,
  Parameter,
  ParameterType,
} from '@aws-sdk/client-ssm';

interface ParameterStoreCache {
  data: Record<string, string>;
  expiry: number;
}

export class ParameterStoreConfigProvider {
  private static logger = new Logger('ParameterStoreConfigProvider');
  private static cache: ParameterStoreCache | null = null;
  private static readonly CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes
  private static readonly AWS_REGION = 'eu-west-1';
  private static readonly MAX_RESULTS = 10; // AWS Parameter Store maximum per request

  /**
   * Environment-specific Parameter Store path mapping
   */
  private static readonly ENVIRONMENT_PATH_MAP = {
    development: '/react/dev/api/',
    staging: '/react/staging/api/',
    production: '/react/prod/api/',
  } as const;

  /**
   * Supported environments for Parameter Store loading
   */
  private static readonly SUPPORTED_ENVIRONMENTS = [
    'development',
    'staging',
    'production',
  ] as const;

  /**
   * Determine if the current environment supports Parameter Store loading
   */
  private static isParameterStoreEnvironment(
    nodeEnv: string | undefined,
  ): boolean {
    if (!nodeEnv) {
      return false;
    }
    return this.SUPPORTED_ENVIRONMENTS.includes(nodeEnv as any);
  }

  /**
   * Get the Parameter Store path prefix for the current environment
   */
  private static getParameterStorePath(nodeEnv: string): string {
    const normalizedEnv = nodeEnv.toLowerCase();

    if (normalizedEnv in this.ENVIRONMENT_PATH_MAP) {
      return this.ENVIRONMENT_PATH_MAP[
        normalizedEnv as keyof typeof this.ENVIRONMENT_PATH_MAP
      ];
    }

    // Fallback to production path for unknown environments
    this.logger.warn(
      `Unknown environment '${nodeEnv}', falling back to production path`,
    );
    return this.ENVIRONMENT_PATH_MAP.production;
  }

  /**
   * Validate environment and return normalized environment name
   */
  private static validateAndNormalizeEnvironment(
    nodeEnv: string | undefined,
  ): string | null {
    if (!nodeEnv) {
      this.logger.warn('NODE_ENV is not set');
      return null;
    }

    const normalizedEnv = nodeEnv.toLowerCase().trim();

    if (!this.isParameterStoreEnvironment(normalizedEnv)) {
      this.logger.log(
        `Environment '${nodeEnv}' does not support Parameter Store loading`,
      );
      return null;
    }

    return normalizedEnv;
  }

  static async loadParametersAndMergeWithEnv(): Promise<Record<string, any>> {
    const nodeEnv = process.env.NODE_ENV;
    const region = process.env.AWS_REGION || this.AWS_REGION;

    this.logger.log(`Environment check: NODE_ENV=${nodeEnv}, region=${region}`);

    // Validate and normalize environment
    const validatedEnv = this.validateAndNormalizeEnvironment(nodeEnv);
    if (!validatedEnv) {
      this.logger.log(
        'Environment does not support Parameter Store loading, skipping',
      );
      return process.env; // Return current env without changes
    }

    // Get environment-specific Parameter Store path
    const parameterStorePath = this.getParameterStorePath(validatedEnv);
    this.logger.log(
      `Parameter Store loading enabled for environment: ${validatedEnv}`,
    );
    this.logger.log(`Using Parameter Store path: ${parameterStorePath}`);

    try {
      // Check if we have cached data that's still valid
      if (this.cache && Date.now() < this.cache.expiry) {
        this.logger.log('Using cached Parameter Store configuration');
        return { ...process.env, ...this.cache.data };
      }

      // Create SSM client with IAM role (no hardcoded credentials)
      const ssmClient = new SSMClient({
        region,
        // IAM role will be used automatically when running on AWS infrastructure
        // No explicit credentials needed
      });

      this.logger.log(`Fetching parameters from path: ${parameterStorePath}`);

      // Load all parameters with pagination support
      const allParameters = await this.loadAllParameters(
        ssmClient,
        parameterStorePath,
      );

      if (allParameters.length === 0) {
        this.logger.warn(`No parameters found at path: ${parameterStorePath}`);
        throw new Error(`No parameters found at path: ${parameterStorePath}`);
      }

      this.logger.log(
        `Retrieved ${allParameters.length} parameters from Parameter Store`,
      );

      // Process parameters and handle different types
      const parameterConfig = this.processParameters(
        allParameters,
        parameterStorePath,
      );

      // Cache the processed configuration
      this.cache = {
        data: parameterConfig,
        expiry: Date.now() + this.CACHE_TTL_MS,
      };

      // Return Parameter Store configuration (will be merged with process.env in bootstrap)
      this.logger.log(
        `Successfully loaded and cached ${allParameters.length} parameters from Parameter Store`,
      );

      // Log sample values for debugging (hide secrets)
      this.logSampleConfiguration(parameterConfig);

      return parameterConfig;
    } catch (error: any) {
      this.logger.error(
        'Failed to load parameters from Parameter Store:',
        error,
      );
      // Return current env without changes when there's an error
      return process.env;
    }
  }

  /**
   * Load all parameters from Parameter Store with pagination support
   */
  private static async loadAllParameters(
    ssmClient: SSMClient,
    parameterStorePath: string,
  ): Promise<Parameter[]> {
    const allParameters: Parameter[] = [];
    let nextToken: string | undefined;

    this.logger.log(
      `Starting parameter loading from path: ${parameterStorePath}`,
    );

    try {
      do {
        this.logger.log(
          `Fetching batch of parameters${nextToken ? ' with pagination token' : ''}`,
        );

        const command = new GetParametersByPathCommand({
          Path: parameterStorePath,
          Recursive: true,
          WithDecryption: true,
          MaxResults: this.MAX_RESULTS,
          NextToken: nextToken,
        });

        const response = await ssmClient.send(command);
        const parameters = response.Parameters || [];

        this.logger.log(
          `Received ${parameters.length} parameters in this batch`,
        );

        allParameters.push(...parameters);
        nextToken = response.NextToken;
      } while (nextToken);

      return allParameters;
    } catch (error) {
      this.logger.error('Error in loadAllParameters:', error);
      throw error;
    }
  }

  /**
   * Process parameters and handle different parameter types
   */
  private static processParameters(
    parameters: Parameter[],
    parameterStorePath: string,
  ): Record<string, string> {
    const config: Record<string, string> = {};

    parameters.forEach((param: Parameter) => {
      if (!param.Name || param.Value === undefined) {
        this.logger.warn(
          `Skipping parameter with missing name or value: ${param.Name}`,
        );
        return;
      }

      // Convert parameter name to environment variable format
      const envKey = param.Name.replace(parameterStorePath, '')
        .replace(/\//g, '_')
        .toUpperCase();

      // Handle different parameter types
      let processedValue = param.Value;

      switch (param.Type) {
        case ParameterType.STRING:
          // Standard string - use as is
          processedValue = param.Value;
          break;

        case ParameterType.SECURE_STRING:
          // Encrypted string - already decrypted by WithDecryption: true
          processedValue = param.Value;
          break;

        case ParameterType.STRING_LIST:
          // Comma-separated list - keep as string for environment variable compatibility
          processedValue = param.Value;
          break;

        default:
          this.logger.warn(
            `Unknown parameter type ${param.Type} for ${param.Name}`,
          );
          processedValue = param.Value;
      }

      config[envKey] = processedValue;
      this.logger.debug(`Mapped ${param.Name} (${param.Type}) -> ${envKey}`);
    });

    return config;
  }

  /**
   * Log sample configuration values for debugging (hide sensitive data)
   */
  private static logSampleConfiguration(config: Record<string, any>): void {
    const sensitiveKeys = [
      'PASSWORD',
      'SECRET',
      'KEY',
      'TOKEN',
      'CREDENTIAL',
      'AUTH',
      'DATABASE_URL',
      'SMTP_PASSWORD',
      'AWS_SECRET_ACCESS_KEY',
      'FIREBASE_SERVICE_ACCOUNT_BASE64',
      'SENTRY_DSN',
    ];

    const sampleKeys = [
      'NODE_ENV',
      'PORT',
      'APP_NAME',
      'AWS_REGION',
      'REDIS_HOST',
    ];
    const sampleConfig: Record<string, string> = {};

    sampleKeys.forEach((key) => {
      if (config[key]) {
        sampleConfig[key] = config[key];
      }
    });

    // Add count of sensitive parameters (without values)
    const sensitiveCount = Object.keys(config).filter((key) =>
      sensitiveKeys.some((sensitiveKey) => key.includes(sensitiveKey)),
    ).length;

    this.logger.log(`Sample configuration: ${JSON.stringify(sampleConfig)}`);
    this.logger.log(
      `Loaded ${sensitiveCount} sensitive parameters (values hidden)`,
    );
    this.logger.log(`Total parameters loaded: ${Object.keys(config).length}`);
  }

  /**
   * Clear the parameter cache (useful for testing or manual refresh)
   */
  static clearCache(): void {
    this.cache = null;
    this.logger.log('Parameter Store cache cleared');
  }

  /**
   * Check if cache is valid
   */
  static isCacheValid(): boolean {
    return this.cache !== null && Date.now() < this.cache.expiry;
  }
}
